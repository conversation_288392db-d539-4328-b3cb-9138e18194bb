NODE_ENV=development
APP_PORT=4000
APP_PREFIX=api/v1
APP_NAME=moonbags
API_URI=http://localhost:4000

MONGO_DATABASE=moonbags
MONGODB_URI=mongodb://mongo1:30001,mongo2:30002,mongo3:30003/?replicaSet=my-replica-set

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

WS_PORT=4001
PINATA_JWT=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QDs3qEqUODmwr8qoiFEci0I0rK93TWtnUoBVfO0C2XI
PINATA_GATEWAY=beige-abstract-pig-256.mypinata.cloud

# EVM config
EVM_RPC_URL=https://rpc.hyperliquid.xyz/evm
SHRO_TOKEN_ADDRESS=0x7718F3529425E09bF103e5dcf35B9BbB2148f420
SHRO_TREASURY_ADDRESS=0x4c3ee0b377e3a2e50d8378bc02fe95841a6d64d8d52bbfa9222a2a6c1982f358
MOONBAGS_LAUNCHPAD_ADDRESS=0xda6fCCed61457224e65927b373f57Dae98bF50a7
MOONBAGS_STAKE_ADDRESS=0x0000000000000000000000000000000000000000
TOKEN_LOCK_ADDRESS=0x0000000000000000000000000000000000000000

# upload image config
IMAGE_STORAGE=pinata
GCP_BUCKET_NAME=moonbags
