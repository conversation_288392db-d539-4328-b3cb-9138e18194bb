import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SharedModule } from '@modules/shared/shared.module';
import { AuthModule } from '@modules/shared/auth/auth.module';
import { Candle, CandleSchema } from './schemas/candle.schema';
import { CandleController } from './candle.controller';
import { CandleRepository } from './repositories/candle.repository';
import { CandleService } from './candle.service';

@Module({
  imports: [
    SharedModule,
    MongooseModule.forFeature([{ name: Candle.name, schema: CandleSchema }]),
    AuthModule,
  ],
  controllers: [CandleController],
  providers: [CandleRepository, CandleService],
  exports: [CandleService],
})
export class CandleModule {}
