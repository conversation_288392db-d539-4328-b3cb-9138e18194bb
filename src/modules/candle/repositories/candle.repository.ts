import { Injectable } from '@nestjs/common';
import { BaseRepository } from '@shares/base.repository';
import { Candle, CandleDocument } from '../schemas/candle.schema';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel } from 'mongoose';
import { CandleRecord } from '../candle.interface';
import { RESOLUTION_SECOND } from '../candle.const';

@Injectable()
export class CandleRepository extends BaseRepository<Candle> {
  constructor(@InjectModel(Candle.name) model: PaginateModel<CandleDocument>) {
    super(model);
  }

  async getPreviousCandle(
    tokenAddress: string,
    time: number,
  ): Promise<CandleRecord | null> {
    const candles = await this.find();
    const targetTime = new Date(time).getTime();
    const filteredCandles = candles.filter(
      (candle) =>
        candle.tokenAddress === tokenAddress &&
        candle.resolution === RESOLUTION_SECOND,
    );

    filteredCandles.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );
    for (let i = 0; i < filteredCandles.length; i++) {
      const candleTime = new Date(filteredCandles[i].timestamp).getTime();
      if (candleTime < targetTime) {
        const candle: CandleRecord = {
          tokenAddress: filteredCandles[i].tokenAddress,
          resolution: filteredCandles[i].resolution,
          open: filteredCandles[i].price.open.toString(),
          close: filteredCandles[i].price.close.toString(),
          low: filteredCandles[i].price.low.toString(),
          high: filteredCandles[i].price.high.toString(),
          openUsd: filteredCandles[i].priceUsd.open.toString(),
          closeUsd: filteredCandles[i].priceUsd.close.toString(),
          lowUsd: filteredCandles[i].priceUsd.low.toString(),
          highUsd: filteredCandles[i].priceUsd.high.toString(),
          timestamp: filteredCandles[i].timestamp,
          volumeBase: filteredCandles[i].volumeBase,
          volumeQuote: filteredCandles[i].volumeQuote,
          volumeUsd: filteredCandles[i].volumeUsd,
        };
        return candle;
      }
    }
    return null;
  }
}
