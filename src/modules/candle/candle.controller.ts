import { Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CandleService } from './candle.service';
import { GetCandlesQueryDto, ResponseCandlesDto } from './dtos/candle.dto';
import { plainToInstance } from 'class-transformer';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

const CACHE_TTL = 1000; // 1 second

@ApiTags('Candle')
@Controller('candle')
export class CandleController {
  constructor(private readonly candleService: CandleService) {}

  @Get()
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  async getCandles(@Query() input: GetCandlesQueryDto) {
    const candles = await this.candleService.getCandles(
      input.limit,
      input.to,
      input.tokenAddress,
      input.resolution,
      input.queryBy,
    );
    return plainToInstance(
      ResponseCandlesDto,
      {
        candles: candles,
        timestampOffset: input.to,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }
}
