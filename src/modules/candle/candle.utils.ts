import { Candle } from './schemas/candle.schema';
import BigNumber from 'bignumber.js';

BigNumber.config({
  EXPONENTIAL_AT: 32,
});

export const standardizeCandleTime = (
  timestampMs: number,
  resolution: number,
): number => {
  return Math.floor(timestampMs / resolution) * resolution;
};

export const calcListMissCandleBetween = (
  fromCandle: Candle,
  toCandle: Candle,
): Candle[] => {
  if (fromCandle.tokenAddress !== toCandle.tokenAddress) {
    console.error('Cannot calculate 2 candles with different poolId', {
      fromCandle,
      toCandle,
    });
    throw new Error('Cannot calculate 2 candles with different poolId');
  }
  const resolution = +fromCandle.resolution;
  const nextCandleTime = fromCandle.timestamp + resolution;

  const missCandles: Candle[] = [];
  for (
    let time = nextCandleTime;
    time < toCandle.timestamp;
    time += resolution
  ) {
    missCandles.push({
      tokenAddress: fromCandle.tokenAddress,
      timestamp: time,
      resolution: fromCandle.resolution,
      price: fromCandle.price,
      priceUsd: fromCandle.priceUsd,
      marketCap: fromCandle.marketCap,
      marketCapUsd: fromCandle.marketCapUsd,
      volumeBase: '0',
      volumeQuote: '0',
      volumeUsd: '0',
    } as Candle);
  }
  return missCandles;
};

export const formatNumber = (value: string): string => {
  const numberValue = parseFloat(value);

  if (isNaN(numberValue)) {
    return '0';
  }

  return numberValue.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};
// export const fillListMissCandleBetween = (fromCandle: Candles, toCandle: Candles, resolution: number): Candles[] => {
//   const resolutionInSeconds = resolution * 1000;
//   const nextCandleTime = fromCandle.timestamp + resolutionInSeconds;

//   const missCandles: Candle[] = [];
//   for (let time = nextCandleTime; time < toCandle.timestamp; time += resolutionInSeconds) {
//     missCandles.push({
//       timestamp:time,
//       resolution: resolution,

//     } as Candle);
//   }
//   return missCandles;
// };
