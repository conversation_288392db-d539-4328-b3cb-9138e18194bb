export interface CandleRecord {
  tokenAddress: string;
  timestamp: number;
  resolution: number;
  open: string;
  close: string;
  low: string;
  high: string;
  openUsd: string;
  closeUsd: string;
  lowUsd: string;
  highUsd: string;
  volumeUsd: string;
  volumeBase: string;
  volumeQuote: string;
}

export interface Candles {
  timestamp: number;
  open: string | number;
  close: string | number;
  low: string | number;
  high: string | number;
  openUsd: string | number;
  closeUsd: string | number;
  lowUsd: string | number;
  highUsd: string | number;
  volumeUsd: string | number;
  volumeBase: string | number;
  volumeQuote: string | number;
}
