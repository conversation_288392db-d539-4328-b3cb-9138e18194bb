import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true,
  },
})
export class SocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer() server: Server;

  afterInit(server: Server) {
    console.log('Init', server._opts);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
  }

  handleConnection(client: Socket, ...args: any[]) {
    console.log(`Client connected: ${client.id}`, args);
  }

  @SubscribeMessage('SUBSCRIBE')
  handleJoinRoom(@ConnectedSocket() client: Socket, @MessageBody() data: any) {
    if (!data.room) {
      return;
    }
    client.join(data.room);
    console.log(`Client ${client.id} joined room: ${data.room}`);
    client.emit('SUBSCRIBE', `${client.id} has joined the room ${data.room}`);
  }

  @SubscribeMessage('UNSUBSCRIBE')
  handleLeaveRoom(@ConnectedSocket() client: Socket, @MessageBody() data: any) {
    if (!data.room) {
      return;
    }
    client.leave(data.room);
    console.log(`Client ${client.id} left room: ${data.room}`);
    client.emit('UNSUBSCRIBE', `${client.id} has left the room ${data.room}`);
  }
}
