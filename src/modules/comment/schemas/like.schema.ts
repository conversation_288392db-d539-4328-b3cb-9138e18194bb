import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type LikeDocument = Like & Document;

@Schema({ timestamps: true })
export class Like extends Document {
  @Prop({ type: Types.ObjectId, ref: 'Comment', required: true })
  commentId: Types.ObjectId;

  @Prop({ ref: 'User', required: true })
  walletAddress: string;

  @Prop({ required: true })
  coinAddress: string;
}

export const LikeSchema = SchemaFactory.createForClass(Like);

LikeSchema.index({ commentId: 1, walletAddress: 1 }, { unique: true });
LikeSchema.index({ coinAddress: 1, walletAddress: 1 });
