import { User } from '@modules/shared/auth/schemas/user.schema';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

export type CommentDocument = Comment & Document;

@Schema({ timestamps: true })
export class Comment extends Document {
  @Prop({ required: true })
  coinAddress: string;

  @Prop({ ref: 'User', required: true })
  walletAddress: string;

  @Prop({ type: Types.ObjectId, ref: 'Comment', default: null })
  parentCommentId: Types.ObjectId | null;

  @Prop({ required: true })
  content: string;

  @Prop({ default: 0 })
  likesCount: number;

  // @Prop({ required: true })
  createdAt: Date;
}

const CommentSchema = SchemaFactory.createForClass(Comment);

CommentSchema.index({ coinAddress: 1, parentCommentId: 1, createdAt: -1 });
CommentSchema.index({ parentCommentId: 1, createdAt: 1 });
CommentSchema.index({ walletAddress: 1 });
CommentSchema.index({ coinAddress: 1, createdAt: -1 });

CommentSchema.plugin(mongoosePaginate);
CommentSchema.virtual('user', {
  ref: User.name,
  localField: 'walletAddress',
  foreignField: 'walletAddress',
  justOne: true,
});

export { CommentSchema };
