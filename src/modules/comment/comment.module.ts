import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Comment, CommentSchema, Like, LikeSchema } from './schemas';
import { SharedModule } from '@modules/shared/shared.module';
import { CommentController } from './comment.controller';
import { CommentRepository } from './repositories/comment.repository';
import { CommentService } from './comment.service';
import { UserRepository } from '@modules/shared/auth/repositories/user.repository';
import { User, UserSchema } from '@modules/shared/auth/schemas/user.schema';

@Module({
  imports: [
    SharedModule,
    MongooseModule.forFeature([
      { name: Comment.name, schema: CommentSchema },
      { name: Like.name, schema: LikeSchema },
      { name: User.name, schema: UserSchema },
    ]),
  ],
  controllers: [CommentController],
  providers: [CommentRepository, CommentService, UserRepository],
})
export class CommentModule {}
