import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Expose } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  MaxLength,
  IsOptional,
  Min,
  Max,
  <PERSON>N<PERSON>ber,
} from 'class-validator';
import { PaginatedResponseDto } from '@shares/dtos/paginated-response.dto';
import { PaginateResult } from 'mongoose';

export class CreateCommentDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  content: string;
}

export class CreateReplyDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  content: string;
}

export class CommentQueryDto {
  @ApiPropertyOptional({ default: 1 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ default: 10 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  limit?: number = 10;
}

export class UserDto {
  @Expose()
  @ApiProperty()
  walletAddress: string;

  @Expose()
  @ApiProperty()
  name: string;

  @Expose()
  @ApiProperty()
  photo: string;

  @Expose()
  @ApiProperty()
  username: string;

  constructor(user: any) {
    this.walletAddress = user?.walletAddress;
    this.name = user?.name;
    this.photo = user?.photo;
    this.username = user?.username;
  }
}

export class CommentDto {
  @Expose()
  @ApiProperty()
  @Transform(({ obj }) => obj._id)
  id: string;

  @Expose()
  @ApiProperty()
  content: string;

  @Expose()
  @ApiProperty()
  likesCount: number;

  @Expose()
  @ApiProperty()
  createdAt: Date;

  @Expose()
  @ApiProperty()
  updatedAt: Date;

  @Expose()
  @ApiProperty()
  user: UserDto;

  constructor(comment: any) {
    this.id = comment._id;
    this.content = comment.content;
    this.likesCount = comment.likesCount;
    this.createdAt = comment.createdAt;
    this.updatedAt = comment.updatedAt;
    this.user = new UserDto(comment.user);
  }
}

export class ParentCommentDto extends CommentDto {
  @Expose()
  @ApiProperty({ type: [CommentDto] })
  replies: CommentDto[];

  @Expose()
  @ApiProperty()
  totalReplies: number;

  constructor(comment: any, replyData: any) {
    super(comment);
    const replyInfo = replyData?.find(
      (r) => r._id.toString() === comment._id.toString(),
    );
    this.replies = (replyInfo?.replies || []).map(
      (reply) => new CommentDto(reply),
    );
    this.totalReplies = replyInfo?.totalReplies || 0;
  }
}

export class ParentCommentResponseDto extends PaginatedResponseDto<ParentCommentDto> {
  @Expose()
  @ApiProperty({ type: [ParentCommentDto] })
  docs: ParentCommentDto[];

  constructor(parentResult: PaginateResult<any>, replies: any[]) {
    super(parentResult);
    this.docs = parentResult.docs.map(
      (doc) => new ParentCommentDto(doc, replies),
    );
  }
}

export class ReplyResponseDto extends PaginatedResponseDto<CommentDto> {
  @Expose()
  @ApiProperty({ type: [CommentDto] })
  docs: CommentDto[];

  constructor(replyResult: PaginateResult<any>) {
    super(replyResult);
    this.docs = replyResult.docs.map((doc) => new CommentDto(doc));
  }
}

export class UserLikesResponseDto {
  @Expose()
  @ApiProperty({ type: [String] })
  likes: string[];

  constructor(likes: any[]) {
    this.likes = likes.map((like) => like.commentId.toString());
  }
}
