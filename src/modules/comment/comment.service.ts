import {
  BadRequestException,
  Injectable,
  NotFoundException,
  OnApplicationBootstrap,
} from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { Connection, Model, Types } from 'mongoose';
import {
  CommentDto,
  CommentQueryDto,
  CreateCommentDto,
  CreateReplyDto,
  ParentCommentResponseDto,
  ReplyResponseDto,
  UserLikesResponseDto,
} from './dtos/comment.dto';
import { CommentRepository } from './repositories/comment.repository';
import { Comment } from './schemas/comment.schema';
import { Like } from './schemas/like.schema';
import { MongoUtils } from 'src/utils/mongo.utils';
import { Kafka, Producer } from 'kafkajs';
import { ConfigService } from '@nestjs/config';
import { KafkaTopic } from 'src/shares/enums/kafka.enum';

@Injectable()
export class CommentService implements OnApplicationBootstrap {
  private kafka: Kafka;
  private producer: Producer;

  constructor(
    @InjectConnection()
    private readonly connection: Connection,
    @InjectModel(Comment.name) private readonly commentModel: Model<Comment>,
    @InjectModel(Like.name) private readonly likeModel: Model<Like>,
    private readonly commentRepository: CommentRepository,
    private readonly configService: ConfigService,
  ) {
    this.kafka = new Kafka({
      clientId: this.configService.getOrThrow<string>('kafka.clientId'),
      brokers: this.configService.getOrThrow<string[]>('kafka.brokers'),
    });
    this.producer = this.kafka.producer({
      allowAutoTopicCreation: true,
    });
  }

  async onApplicationBootstrap() {
    await Promise.all([this.producer.connect()]);
  }

  async createParentComment(
    coinAddress: string,
    comment: CreateCommentDto,
    walletAddress: string,
  ): Promise<CommentDto> {
    const newComment = new this.commentModel({
      content: comment.content,
      coinAddress,
      walletAddress: walletAddress,
      parentCommentId: null,
    });

    const savedComment = await newComment.save();

    const populatedComment = await this.commentModel
      .findById(savedComment._id)
      .populate('user')
      .lean();

    await this.broadcastComment({
      tokenAddress: coinAddress,
      walletAddress: walletAddress,
      timestamp: savedComment.createdAt.getTime(),
    });

    return new CommentDto(populatedComment);
  }

  async broadcastComment(payload: any) {
    try {
      await this.producer.send({
        topic: KafkaTopic.COMMENT_CREATED,
        messages: [{ value: JSON.stringify(payload) }],
      });
    } catch (error: any) {
      console.error(`broadcastComment throw error: ${error?.message}`);
    }
  }

  async createReply(
    parentCommentId: string,
    reply: CreateReplyDto,
    walletAddress: string,
  ): Promise<CommentDto> {
    const parentComment = await this.commentModel.findById(parentCommentId);
    if (!parentComment) {
      throw new NotFoundException('Parent comment not found');
    }

    if (parentComment.parentCommentId) {
      throw new BadRequestException('Cannot reply to a reply');
    }

    const newReply = new this.commentModel({
      content: reply.content,
      coinAddress: parentComment.coinAddress,
      walletAddress: walletAddress,
      parentCommentId: new Types.ObjectId(parentCommentId),
    });

    const savedReply = await newReply.save();

    const populatedReply = await this.commentModel
      .findById(savedReply._id)
      .populate('user', '-_id name photo address username')
      .lean();

    await this.broadcastComment({
      tokenAddress: parentComment.coinAddress,
      walletAddress: walletAddress,
      timestamp: savedReply.createdAt.getTime(),
    });

    return new CommentDto(populatedReply);
  }

  async like(commentId: string, walletAddress: string) {
    if (!commentId) {
      throw new BadRequestException('Comment ID is required');
    }

    const comment = await this.commentModel.findById(commentId);
    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    try {
      await MongoUtils.withTransaction(this.connection, async (session) => {
        const like = new this.likeModel({
          commentId: new Types.ObjectId(commentId),
          walletAddress: walletAddress,
          coinAddress: comment.coinAddress,
        });
        await like.save({ session });

        await this.commentModel.updateOne(
          { _id: commentId },
          { $inc: { likesCount: 1 } },
          { session },
        );
      });

      return this.commentModel.findById(commentId);
    } catch (error) {
      if (error.code === 11000) {
        throw new BadRequestException('User has already liked this comment');
      }
      throw error;
    }
  }

  async unlike(commentId: string, walletAddress: string) {
    if (!commentId) {
      throw new BadRequestException('Comment ID is required');
    }

    const comment = await this.commentModel.findById(commentId);
    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    try {
      await MongoUtils.withTransaction(this.connection, async (session) => {
        const result = await this.likeModel.deleteOne(
          {
            commentId: new Types.ObjectId(commentId),
            walletAddress: walletAddress,
          },
          { session },
        );

        if (result.deletedCount === 0) {
          throw new BadRequestException('User has not liked this comment');
        }

        await this.commentModel.updateOne(
          { _id: commentId },
          { $inc: { likesCount: -1 } },
          { session },
        );
      });

      return this.commentModel.findById(commentId);
    } catch (error) {
      throw error;
    }
  }

  async findParentComments(
    coinAddress: string,
    query: CommentQueryDto,
  ): Promise<ParentCommentResponseDto> {
    if (!coinAddress) {
      throw new BadRequestException('Coin address is required');
    }

    const { page = 1, limit = 10 } = query;

    const parentResult = await this.commentRepository.paginate(
      { coinAddress, parentCommentId: null },
      {
        page,
        limit,
        sort: { createdAt: -1 },
        populate: {
          path: 'user',
          select: '-_id name photo address username',
        },
        lean: true,
      },
    );

    const parentIds = parentResult.docs.map(
      (p) => new Types.ObjectId(p._id as string),
    );

    const replies = await this.getRepliesForParents(parentIds);
    return new ParentCommentResponseDto(parentResult, replies);
  }

  async findReplies(
    parentCommentId: string,
    query: CommentQueryDto,
  ): Promise<ReplyResponseDto> {
    const { page = 1, limit = 10 } = query;

    const result = await this.commentRepository.paginate(
      { parentCommentId: new Types.ObjectId(parentCommentId) },
      {
        page,
        limit,
        sort: { createdAt: -1 },
        populate: {
          path: 'user',
          select: '-_id name photo address username',
        },
      },
    );

    return new ReplyResponseDto(result);
  }

  private async getRepliesForParents(parentIds: Types.ObjectId[], limit = 3) {
    const replies = await this.commentModel
      .find({
        parentCommentId: { $in: parentIds },
      })
      .sort({ createdAt: -1 })
      .populate('user', '-_id name photo address username')
      .lean();

    return parentIds.map((parentId) => {
      const parentReplies = replies
        .filter(
          (reply) => reply.parentCommentId.toString() === parentId.toString(),
        )
        .slice(0, limit);

      return {
        _id: parentId,
        replies: parentReplies,
        totalReplies: replies.filter(
          (reply) => reply.parentCommentId.toString() === parentId.toString(),
        ).length,
      };
    });
  }

  async getUserLikes(
    coinAddress: string,
    walletAddress: string,
  ): Promise<UserLikesResponseDto> {
    if (!coinAddress) {
      throw new BadRequestException('Coin address is required');
    }

    const likes = await this.likeModel
      .find({
        coinAddress,
        walletAddress,
      })
      .lean();

    return new UserLikesResponseDto(likes);
  }
}
