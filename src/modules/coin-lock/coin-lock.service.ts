import { Injectable, NotFoundException } from '@nestjs/common';
import { CoinLockRepository } from './repositories/coin-lock.repository';
import { PaginationDto } from '@shares/dtos/pagination.dto';
import { CoinLockResponseDto } from './dtos/coin-lock.dto';
import { PaginatedResponseDto } from '@shares/dtos/paginated-response.dto';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class CoinLockService {
  constructor(private readonly coinLockRepository: CoinLockRepository) {}

  async findByLocker(locker: string, query: PaginationDto) {
    const { page, limit } = query;

    const result = await this.coinLockRepository.paginate(
      { locker },
      {
        sort: { createdAt: -1 },
        page,
        limit,
      },
    );

    const responseDto = new PaginatedResponseDto(result);
    responseDto.docs = result.docs.map((doc) =>
      plainToInstance(CoinLockResponseDto, doc, {
        excludeExtraneousValues: true,
      }),
    );

    return responseDto;
  }

  async findOneByLockerAndContractId(locker: string, contractId: string) {
    const coinLock = await this.coinLockRepository.findOne({
      locker,
      contractId,
    });
    if (!coinLock) {
      throw new NotFoundException('Coin lock not found');
    }

    return coinLock;
  }

  async getTotalLockedAmountByTokenAddress(tokenAddress: string) {
    const coinLock = await this.coinLockRepository.aggregate([
      {
        $match: {
          'tokenInfo.tokenAddress': tokenAddress,
          closed: false,
        },
      },
      {
        $group: {
          _id: '$tokenInfo.tokenAddress',
          totalLockedAmount: { $sum: '$amount' },
        },
      },
    ]);

    return coinLock[0]?.totalLockedAmount || 0;
  }

  async getLockedListByTokenAddress(
    tokenAddress: string,
    query: PaginationDto,
  ) {
    const { page, limit } = query;

    return await this.coinLockRepository.paginate(
      { 'tokenInfo.tokenAddress': tokenAddress, closed: false },
      {
        sort: { amount: -1, _id: 1 },
        page,
        limit,
      },
    );
  }
}
