import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { CoinLock, CoinLockDocument } from '../schemas/coin-lock.schema';

@Injectable()
export class CoinLockRepository extends BaseRepository<CoinLock> {
  constructor(
    @InjectModel(CoinLock.name) model: PaginateModel<CoinLockDocument>,
  ) {
    super(model);
  }
}
