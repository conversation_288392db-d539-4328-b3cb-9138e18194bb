import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Decimal128 } from 'bson';
import mongoosePaginate from 'mongoose-paginate-v2';

export type CoinLockDocument = CoinLock & Document;

@Schema({ _id: false })
export class TokenInfo {
  @Prop()
  tokenAddress: string;

  @Prop()
  symbol: string;

  @Prop()
  logoUri: string;
}

@Schema({
  timestamps: true,
  collection: 'coin_lock',
})
export class CoinLock {
  @Prop()
  contractId: string;

  @Prop()
  locker: string;

  @Prop()
  recipient: string;

  @Prop({ type: TokenInfo })
  tokenInfo: TokenInfo;

  @Prop()
  amount: Decimal128;

  @Prop()
  fee: Decimal128;

  @Prop()
  startTime: number;

  @Prop()
  endTime: number;

  @Prop()
  closed: boolean;
}

export const CoinLockSchema = SchemaFactory.createForClass(CoinLock);

CoinLockSchema.plugin(mongoosePaginate);

CoinLockSchema.index({ contractId: 1 }, { unique: true });
CoinLockSchema.index({ locker: 1, contractId: 1 });
CoinLockSchema.index({ locker: 1, createdAt: -1 });
CoinLockSchema.index({ 'tokenInfo.tokenAddress': 1 });
