import { GetTokenPriceUsdResponseDto } from '@modules/shared/metadata/dtos/token-price.dto';
import { MetadataService } from '@modules/shared/metadata/metadata.service';
import { Controller, Get } from '@nestjs/common';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';

@Controller('metadata')
export class MetadataController {
  constructor(private readonly metadataService: MetadataService) {}

  @Get('token-prices/hype-usd')
  @ApiOperation({ summary: 'Get HYPE token price in USD' })
  @ApiOkResponse({ type: GetTokenPriceUsdResponseDto })
  async getHypeTokenUsd(): Promise<GetTokenPriceUsdResponseDto> {
    const tokenPrice = await this.metadataService.getHypeTokenUsd();
    return plainToInstance(GetTokenPriceUsdResponseDto, tokenPrice);
  }
}
