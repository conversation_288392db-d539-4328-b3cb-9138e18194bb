import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { LoginRequestDto } from './dtos/auth.dto';
import { AuthService } from './auth.service';
import { RefreshTokenGuard } from './guards/refreshToken.guard';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  async login(@Body() body: LoginRequestDto) {
    await this.authService.verifySignature(
      body.signature,
      body.address,
      body.signMessage,
    );

    await this.authService.saveUserByAddress(body.address);
    const accessToken = await this.authService.generateAccessToken(
      body.address,
    );
    const refreshToken = await this.authService.generateRefreshToken(
      body.address,
    );
    return { accessToken, refreshToken };
  }

  @ApiBearerAuth()
  @UseGuards(RefreshTokenGuard)
  @Post('refresh-token')
  async refreshToken(@Req() req) {
    try {
      return this.authService.refreshToken(req);
    } catch (error) {
      console.log('Error refreshToken: ', error);
      return null;
    }
  }
}
