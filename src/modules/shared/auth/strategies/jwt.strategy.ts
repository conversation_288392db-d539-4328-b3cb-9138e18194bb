import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { JwtPayload } from '@shares/dtos/jwt-payload.dto';
import {
  ExtractJwt,
  Strategy,
  StrategyOptionsWithoutRequest,
  StrategyOptionsWithRequest,
} from 'passport-jwt';

@Injectable()
export class JWTStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(private readonly configService: ConfigService) {
    const options: StrategyOptionsWithRequest | StrategyOptionsWithoutRequest =
      {
        jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
        ignoreExpiration: false,
        secretOrKey: configService.get('auth.jwtSecret'),
      };
    super(options);
  }

  async validate(payload: JwtPayload) {
    return {
      address: payload.address,
    };
  }
}
