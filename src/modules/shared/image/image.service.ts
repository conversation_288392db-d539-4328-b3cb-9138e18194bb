import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PinataSDK } from 'pinata-web3';
import { Storage } from '@google-cloud/storage';
import { GCP, PINATA } from '@shares/constants/image.constant';

@Injectable()
export class ImageService {
  private storage: Storage | PinataSDK;
  private storageConfig: string;
  constructor(private readonly configService: ConfigService) {
    const storageConfig = this.configService.getOrThrow('imageStorage');
    if (storageConfig === PINATA) {
      this.storage = new PinataSDK({
        pinataJwt: this.configService.getOrThrow('pinata.jwt'),
      });
    } else {
      this.storage = new Storage();
    }
    this.storageConfig = storageConfig;
  }

  async uploadMedia(file: Express.Multer.File) {
    try {
      switch (this.storageConfig) {
        case PINATA:
          return await this.uploadPinata(file);
        case GCP:
          return await this.uploadGcp(file);
      }
    } catch (error) {
      console.log(
        `Upload image ${file.originalname} to ${this.storageConfig} error: `,
        error,
      );
      throw new BadRequestException(error);
    }
  }

  private async uploadPinata(file: Express.Multer.File) {
    const blob: any = new Blob([file.buffer], { type: file.mimetype });
    const storage = this.storage as PinataSDK;
    const { IpfsHash } = await storage.upload.file(blob).addMetadata({
      name: file.originalname,
    });
    const mediaUrl = `https://ipfs.io/ipfs/${IpfsHash}`;
    return mediaUrl;
  }

  private async uploadGcp(file: Express.Multer.File) {
    const bucketName = this.configService.get('gcp.bucketName');
    const filePath = `token/evm/${file.originalname}`;
    const storage = this.storage as Storage;
    const bucket = storage.bucket(bucketName);
    await bucket.file(filePath).save(file.buffer);
    return `https://storage.googleapis.com/${bucketName}/${filePath}`;
  }
}
