import {
  BadRequestException,
  Controller,
  FileTypeValidator,
  MaxFileSizeValidator,
  ParseFilePipe,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { ImageService } from './image.service';
import { UploadImageDto, UploadImageResponseDto } from './dto/image.dto';
import { JwtAuthGuard } from '../auth/guards/auth.guard';

@ApiTags('Upload ')
@Controller('upload')
export class ImageController {
  constructor(private readonly pinataService: ImageService) {}

  @ApiBearerAuth()
  @Post('image')
  @UseInterceptors(FileInterceptor('image'))
  @ApiOperation({ summary: 'Upload image to <PERSON>nata' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'File image upload',
    type: UploadImageDto,
  })
  @UseGuards(JwtAuthGuard)
  async uploadImage(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }),
          new FileTypeValidator({ fileType: 'image/(jpg|jpeg|png|gif|webp)' }),
        ],
        exceptionFactory: (error) => new BadRequestException(error),
      }),
    )
    file: Express.Multer.File,
  ) {
    const imageUrl = await this.pinataService.uploadMedia(file);
    return plainToInstance(
      UploadImageResponseDto,
      { imageUrl },
      {
        excludeExtraneousValues: true,
      },
    );
  }
}
