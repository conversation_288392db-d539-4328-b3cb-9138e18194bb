import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import configurations from 'src/configs';
import { EmitterModule } from './emitter/emitter.module';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { ImageModule } from './image/image.module';
import { ScheduleModule } from '@nestjs/schedule';
import { MetadataModule } from '@modules/shared/metadata/metadata.module';
import { CacheModule } from '@nestjs/cache-manager';
import KeyvRedis from '@keyv/redis';
import { AuthModule } from '@modules/shared/auth/auth.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: configurations,
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get('mongo.uri'),
      }),
      inject: [ConfigService],
    }),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const host = configService.get('redis.host');
        const port = configService.get('redis.port');
        const password = configService.get('redis.password');
        const url = `redis://:${password}@${host}:${port}`;
        return {
          config: {
            url,
          },
        };
      },
      inject: [ConfigService],
    }),
    ScheduleModule.forRoot(),
    EmitterModule,
    ImageModule,
    MetadataModule,
    AuthModule,
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const host = configService.get('redis.host');
        const port = configService.get('redis.port');
        const db = configService.get('redis.db');
        const password = configService.get('redis.password');
        return {
          stores: [new KeyvRedis(`redis://:${password}@${host}:${port}/${db}`)],
          url: `redis://:${password}@${host}:${port}/${db}`,
          prefix: 'moonbags',
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [],
  exports: [ConfigModule, EmitterModule, ImageModule, CacheModule, AuthModule],
})
export class SharedModule {}
