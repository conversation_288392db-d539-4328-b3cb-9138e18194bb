import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Decimal128 } from 'bson';

export type StakingUserDocument = StakingUser & Document;

@Schema({ timestamps: true, collection: 'staking_users' })
export class StakingUser extends Document {
  @Prop()
  stakingCoinAddress: string;

  @Prop()
  userAddress: string;

  @Prop()
  stakedAmount: Decimal128;

  @Prop()
  rewardClaimed: Decimal128;

  @Prop()
  lastStake: number;
}

export const StakingUserSchema = SchemaFactory.createForClass(StakingUser);
StakingUserSchema.plugin(mongoosePaginate);
StakingUserSchema.index(
  { stakingCoinAddress: 1, userAddress: 1 },
  { background: true },
);
