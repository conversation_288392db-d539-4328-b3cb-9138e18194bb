import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Expose, Transform, Type, Exclude } from 'class-transformer';
import { PaginatedResponseDto } from '@shares/dtos/paginated-response.dto';
import { StakingUserDto } from '@modules/staking/dtos/entities.dto';
import { CoinResponseDto } from '@modules/coin/dtos/coin.dto';
import { toStringBN } from 'src/utils/number.utils';

@Exclude()
export class StakingUserResDto extends StakingUserDto {
  @Expose()
  @ApiProperty({
    type: String,
    description: 'Distributed reward amount',
    example: '10',
  })
  @Transform(({ value }) => value?.toString() || '0')
  distributedAmount: string;
}

export class PaginationStakingUserResDto extends PaginatedResponseDto<StakingUserResDto> {
  @Expose()
  @ApiProperty({ type: [StakingUserResDto] })
  @Type(() => StakingUserResDto)
  docs: StakingUserResDto[];
}

export class StakingCoinResDto extends CoinResponseDto {
  @Expose()
  @ApiProperty({
    type: String,
    description: 'Staked amount',
  })
  includeStakedAmount?: string;

  @Expose()
  @ApiProperty({
    type: String,
    description: 'Reward amount',
  })
  includeRewardAmount?: string;
}
export class PaginationStakingCoinResDto extends PaginatedResponseDto<StakingCoinResDto> {
  @Expose()
  @ApiProperty({ type: [StakingCoinResDto] })
  @Type(() => StakingCoinResDto)
  docs: StakingCoinResDto[];
}

export class StakedSHROCoinResDto extends OmitType(StakingCoinResDto, [
  'volumeUsd24h',
]) {
  @Expose()
  @ApiProperty({
    type: String,
    description: 'Total volume 24h of all coins',
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  totalVolumeUsd24hAllCoin: string;
}
