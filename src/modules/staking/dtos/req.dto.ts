import { ApiPropertyOptional } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class StakingUserQueryDto {
  @ApiPropertyOptional({ default: 1 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ default: 10 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional()
  @IsOptional()
  stakingPoolAddress?: string;

  @ApiPropertyOptional()
  @IsOptional()
  stakingCoinAddress?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return [];
    if (Array.isArray(value)) {
      return value.map((item) => item.trim());
    }
    return [value?.trim()];
  })
  userAddresses?: string[];
}
