import { Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { CoinService } from '@modules/coin/coin.service';
import { ListCoinDto } from '@modules/coin/dtos/coin.dto';
import {
  PaginationStakingCoinResDto,
  StakedSHROCoinResDto,
} from '@modules/staking/dtos/res.dto';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

const CACHE_TTL = 1000; //1 second

@ApiTags('Staking Coin')
@Controller('staking')
export class CoinController {
  constructor(private readonly coinService: CoinService) {}

  @Get('coins')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get coin list can be staked' })
  @ApiResponse({
    status: 200,
    description: 'Return list of staking coins',
    type: PaginationStakingCoinResDto,
  })
  async getStakedCoins(
    @Query() query: ListCoinDto,
  ): Promise<PaginationStakingCoinResDto> {
    const data = await this.coinService.getStakedTokens(query);
    return plainToInstance(PaginationStakingCoinResDto, data, {
      excludeExtraneousValues: true,
    });
  }

  @Get('shro')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get shro coin' })
  @ApiResponse({
    status: 200,
    description: 'Return staked SHRO coin',
    type: StakedSHROCoinResDto,
  })
  @ApiQuery({
    name: 'includeByWalletAddress',
    type: String,
    required: false,
  })
  async getStakedSHROCoin(
    @Query('includeByWalletAddress') includeByWalletAddress?: string,
  ): Promise<StakedSHROCoinResDto> {
    const data = await this.coinService.getStakedSHROCoin(
      includeByWalletAddress,
    );
    return plainToInstance(StakedSHROCoinResDto, data, {
      excludeExtraneousValues: true,
    });
  }
}
