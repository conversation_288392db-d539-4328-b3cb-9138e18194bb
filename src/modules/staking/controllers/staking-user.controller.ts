import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiTags,
} from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { StakingUserService } from '@modules/staking/services';
import { StakingUserQueryDto } from '@modules/staking/dtos/req.dto';
import {
  PaginationStakingUserResDto,
  StakingUserResDto,
} from '@modules/staking/dtos/res.dto';
import { UserAddress } from '@shares/decorators/user.decorator';
import { JwtAuthGuard } from '@modules/shared/auth/guards/auth.guard';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

const CACHE_TTL = 1000; //1 second

@ApiTags('Staking User')
@Controller('staking-user')
export class StakingUserController {
  constructor(private readonly stakingUserService: StakingUserService) {}

  @Get('')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Paginate staking users by user address' })
  @ApiResponse({
    status: 200,
    description: 'Paginated staking users by user address',
    type: PaginationStakingUserResDto,
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async paginateByUserAddress(
    @UserAddress() userAddress: string,
    @Query() query: StakingUserQueryDto,
  ): Promise<PaginationStakingUserResDto> {
    const pagination = await this.stakingUserService.paginateByUserAddress(
      userAddress,
      query,
    );
    return plainToInstance(PaginationStakingUserResDto, pagination);
  }

  @Get(':stakingCoinAddress')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get staking user by staking coin address' })
  @ApiResponse({
    status: 200,
    description: 'Get staking user by staking coin address',
    type: StakingUserResDto,
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async getStakingUserByStakingCoinAddress(
    @UserAddress() userAddress: string,
    @Param('stakingCoinAddress') stakingCoinAddress: string,
  ) {
    try {
      const stakingUser =
        await this.stakingUserService.getStakingUserByStakingCoinAddress(
          userAddress,
          stakingCoinAddress,
        );
      const distributedAmount =
        await this.stakingUserService.getTotalDistributedAmountForUser(
          stakingUser,
        );

      return plainToInstance(StakingUserResDto, {
        ...stakingUser,
        stakingCoinAddress,
        userAddress,
        distributedAmount,
      });
    } catch (error) {
      return null;
    }
  }

  @Get('user-address/:userAddress')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get staking user by user address' })
  @ApiResponse({
    status: 200,
    description: 'Get staking user by user address',
    type: PaginationStakingUserResDto,
  })
  async getPublicStakingUser(
    @Param('userAddress') userAddress: string,
    @Query() query: StakingUserQueryDto,
  ) {
    try {
      const pagination = await this.stakingUserService.paginateByUserAddress(
        userAddress,
        query,
      );

      const stakingUsersWithDistributedAmount = await Promise.all(
        pagination.docs.map(async (stakingUser) => {
          const distributedAmount =
            await this.stakingUserService.getTotalDistributedAmountForUser(
              stakingUser,
            );
          return { ...stakingUser.toObject(), distributedAmount };
        }),
      );

      return plainToInstance(PaginationStakingUserResDto, {
        ...pagination,
        docs: stakingUsersWithDistributedAmount,
      });
    } catch (error) {
      return null;
    }
  }

  @Get(':userAddress/coin/:stakingCoinAddress')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get staking user by coin address' })
  @ApiResponse({
    status: 200,
    description: 'Get staking user by coin address',
    type: StakingUserResDto,
  })
  async getPublicStakingUserByCoinAddress(
    @Param('userAddress') userAddress: string,
    @Param('stakingCoinAddress') stakingCoinAddress: string,
  ) {
    try {
      const stakingUser =
        await this.stakingUserService.getStakingUserByStakingCoinAddress(
          userAddress,
          stakingCoinAddress,
        );
      const distributedAmount =
        await this.stakingUserService.getTotalDistributedAmountForUser(
          stakingUser,
        );

      return plainToInstance(StakingUserResDto, {
        ...stakingUser,
        stakingCoinAddress,
        userAddress,
        distributedAmount,
      });
    } catch (error) {
      return null;
    }
  }

  @Get('/coin/:stakingCoinAddress')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get list staking user by coin address' })
  @ApiResponse({
    status: 200,
    description: 'Get staking user by coin address',
    type: PaginationStakingUserResDto,
  })
  async getPublicListStakingUserByCoinAddress(
    @Param('stakingCoinAddress') stakingCoinAddress: string,
    @Query() query: StakingUserQueryDto,
  ) {
    try {
      const stakingUsers =
        await this.stakingUserService.getPublicListStakingUserByCoinAddress(
          stakingCoinAddress,
          query,
        );

      return plainToInstance(PaginationStakingUserResDto, stakingUsers);
    } catch (error) {
      return null;
    }
  }
}
