import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { StakingUser, StakingUserDocument } from '@modules/staking/schemas';
import { BaseRepository } from '@shares/base.repository';

@Injectable()
export class StakingUserRepository extends BaseRepository<StakingUser> {
  constructor(
    @InjectModel(StakingUser.name)
    stakingUserModel: Model<StakingUserDocument>,
  ) {
    super(stakingUserModel);
  }
}
