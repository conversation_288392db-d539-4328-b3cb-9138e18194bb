import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { StakingUser, StakingUserSchema } from '@modules/staking/schemas';
import { SharedModule } from '@modules/shared/shared.module';
import { CoinModule } from '@modules/coin/coin.module';
import * as controllers from '@modules/staking/controllers';
import * as services from '@modules/staking/services';
import * as repositories from '@modules/staking/repositories';
import { autoImport } from '@shares/utils/common.utils';

@Module({
  imports: [
    SharedModule,
    forwardRef(() => CoinModule),
    MongooseModule.forFeature([
      { name: StakingUser.name, schema: StakingUserSchema },
    ]),
  ],
  controllers: [...autoImport(controllers)],
  providers: [...autoImport(services), ...autoImport(repositories)],
  exports: [...autoImport(services), ...autoImport(repositories)],
})
export class StakingModule {}
