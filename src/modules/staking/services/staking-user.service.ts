import { StakingUserQueryDto } from '@modules/staking/dtos/req.dto';
import { StakingUserRepository } from '@modules/staking/repositories';
import { StakingUserDocument } from '@modules/staking/schemas';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { convertDecToMist, convertMistToDec } from '@shares/utils/common.utils';
import { PaginateResult } from 'mongoose';
import { divideBN, minusBN, multiplyBN, plusBN } from 'src/utils/number.utils';
import { formatEvmAddress } from '@shares/utils/evm-utils';
import { Decimal128 } from 'bson';

const MULTIPLIER = 1e16;
const COIN_DECIMALS = 6;
const HYPE_DECIMALS = 18; // EVM standard decimals for HYPE token

@Injectable()
export class StakingUserService {
  constructor(
    private readonly stakingUserRepository: StakingUserRepository,
    private readonly configService: ConfigService,
  ) {}

  async paginateByUserAddress(
    userAddress: string,
    query: StakingUserQueryDto,
  ): Promise<PaginateResult<StakingUserDocument>> {
    const {
      page = 1,
      limit = 10,
      stakingPoolAddress,
      stakingCoinAddress,
    } = query;

    return this.stakingUserRepository.paginate(
      {
        userAddress,
        ...(stakingPoolAddress && { stakingPoolAddress }),
        ...(stakingCoinAddress && {
          $or: [
            { stakingCoinAddress: formatEvmAddress(stakingCoinAddress) },
            { stakingCoinAddress: stakingCoinAddress },
          ],
        }),
      },
      {
        page,
        limit,
        sort: { createdAt: -1 },
      },
    );
  }

  async getPublicListStakingUserByCoinAddress(
    stakingCoinAddress: string,
    query: StakingUserQueryDto,
  ) {
    const { page = 1, limit = 10 } = query;

    return this.stakingUserRepository.paginate(
      {
        ...(stakingCoinAddress && {
          $or: [
            { stakingCoinAddress: formatEvmAddress(stakingCoinAddress) },
            { stakingCoinAddress: stakingCoinAddress },
          ],
        }),
        ...(query.userAddresses && {
          userAddress: { $in: query.userAddresses },
        }),
        stakedAmount: { $gt: Decimal128.fromString('0') },
      },
      {
        page,
        limit,
        sort: { stakedAmount: -1 },
      },
    );
  }

  async getStakingUserByStakingCoinAddress(
    userAddress: string,
    stakingCoinAddress: string,
  ) {
    const stakingUser = await this.stakingUserRepository.findOne({
      userAddress,
      $or: [
        { stakingCoinAddress: formatEvmAddress(stakingCoinAddress) },
        { stakingCoinAddress: stakingCoinAddress },
      ],
    });

    if (!stakingUser) {
      throw new NotFoundException(
        `Staking user ${userAddress} not found for coin ${stakingCoinAddress}`,
      );
    }

    return stakingUser;
  }

  async getStakingAccountEarnedAmount(accountObjectId: string) {
    if (!accountObjectId) return '0';

    // TODO: Implement EVM staking account data fetching
    console.warn('EVM staking account data fetching not yet implemented');
    const stakingAccountData = null;

    if (
      stakingAccountData.content.dataType !== 'moveObject' ||
      !stakingAccountData.content.fields
    ) {
      throw new Error('Error staking account object data type');
    }

    const stakingAccount = stakingAccountData.content.fields as any;
    return convertMistToDec(stakingAccount.earned);
  }

  async getTotalDistributedAmountForUser(stakingUser: StakingUserDocument) {
    // TODO: Implement EVM staking functionality
    console.warn('EVM staking functionality not yet implemented');
    return '0';
  }
}
