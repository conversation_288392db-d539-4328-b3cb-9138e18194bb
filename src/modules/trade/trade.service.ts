import { Injectable } from '@nestjs/common';
import { TradesDto } from './dtos/trade.dto';
import { TradeRepository } from './repositories/trade.repository';
@Injectable()
export class TradeService {
  constructor(private readonly tradeRepository: TradeRepository) {}
  async getTradesWithPagination(tradesDto: TradesDto) {
    const { limit, page, tokenAddress } = tradesDto;

    const result = await this.tradeRepository.paginate(
      {
        tokenAddress,
      },
      {
        page,
        limit,
        sort: { timestamp: -1 },
      },
    );

    return result;
  }

  async getTopLatestTrades() {
    return this.tradeRepository.find(
      {},
      {},
      { limit: 50, sort: { timestamp: -1, _id: 1 } },
    );
  }
}
