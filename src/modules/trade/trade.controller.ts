import { Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import {
  TradeDetailResponseDto,
  TradesDto,
  TradesResponseDto,
} from './dtos/trade.dto';
import { TradeService } from './trade.service';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';
const CACHE_TTL = 1000; //1 second

@ApiTags('Trades')
@Controller('trades')
export class TradeController {
  constructor(private readonly tradeService: TradeService) {}

  @Get()
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  async getTrades(@Query() tradesDto: TradesDto) {
    const data = await this.tradeService.getTradesWithPagination(tradesDto);
    return plainToInstance(TradesResponseDto, data, {
      excludeExtraneousValues: true,
    });
  }

  @Get('top-trade')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  async getTopLatestTrades() {
    const data = await this.tradeService.getTopLatestTrades();
    return plainToInstance(TradeDetailResponseDto, data, {
      excludeExtraneousValues: true,
    });
  }
}
