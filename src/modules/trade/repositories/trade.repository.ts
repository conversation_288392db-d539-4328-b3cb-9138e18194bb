import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { Trade, TradeDocument } from '../schemas/trade.schema';

@Injectable()
export class TradeRepository extends BaseRepository<Trade> {
  constructor(@InjectModel(Trade.name) model: PaginateModel<TradeDocument>) {
    super(model);
  }
}
