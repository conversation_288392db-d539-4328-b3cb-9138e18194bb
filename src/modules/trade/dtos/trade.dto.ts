import { ApiProperty } from '@nestjs/swagger';
import { PaginatedResponseDto } from '@shares/dtos/paginated-response.dto';
import { PaginationDto } from '@shares/dtos/pagination.dto';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { TradeType } from '../schemas/trade.schema';

export class TradesDto extends PaginationDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  tokenAddress: string;
}

export class TradeDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  slot: number;

  @Expose()
  hash: string;

  @Expose()
  tokenAddress: string;

  @Expose()
  index: number;

  @Expose()
  maker: string;

  @Expose()
  tradeType: TradeType;

  @Expose()
  @Type(() => String)
  baseAmount: string;

  @Expose()
  @Type(() => String)
  quoteAmount: string;

  @Expose()
  @Type(() => String)
  price: string;

  @Expose()
  @Type(() => String)
  priceUsd: string;

  @Expose()
  @Type(() => String)
  volume: string;

  @Expose()
  @Type(() => String)
  volumeUsd: string;

  @Expose()
  timestamp: number;

  @Expose()
  @Type(() => String)
  virtualHypeReserves: string;

  @Expose()
  @Type(() => String)
  virtualTokenReserves: string;

  @Expose()
  @Type(() => String)
  realHypeReserves: string;

  @Expose()
  @Type(() => String)
  realTokenReserves: string;

  @Expose()
  createdAt: string;

  @Expose()
  updatedAt: string;

  @Expose()
  tokenSymbol: string;
}

export class TradesResponseDto extends PaginatedResponseDto<TradeDetailResponseDto> {
  @Expose()
  @ApiProperty({ type: [TradeDetailResponseDto] })
  @Type(() => TradeDetailResponseDto)
  docs: TradeDetailResponseDto[];
}
