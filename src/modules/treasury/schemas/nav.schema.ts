import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Decimal128 } from 'bson';

export type NavDocument = Nav & Document;

@Schema({ timestamps: true, collection: 'navs' })
export class Nav extends Document {
  @Prop({ required: true })
  treasuryAddress: string;

  @Prop({ required: true })
  tokenAddress: string;

  @Prop({ required: true, default: 0 })
  amount: number;

  @Prop({ required: false, default: new Decimal128('0') })
  marketValue: Decimal128;
}

const NavSchema = SchemaFactory.createForClass(Nav);
NavSchema.index({ treasuryAddress: 1, tokenAddress: 1 }, { unique: true });
NavSchema.index({ tokenAddress: 1 });
NavSchema.index({ treasuryAddress: 1 });
NavSchema.index(
  { marketValue: -1, treasuryAddress: 1, tokenAddress: 1 },
  { background: true },
);
NavSchema.plugin(mongoosePaginate);

export { NavSchema };
