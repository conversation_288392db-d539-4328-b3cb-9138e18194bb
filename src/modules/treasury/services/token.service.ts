import { Injectable } from '@nestjs/common';
import { Token, TToken } from '../schemas/token.schema';
import { TokenRepository } from '../repositories/token.repository';

@Injectable()
export class TokenService {
  constructor(private readonly tokenRepository: TokenRepository) {}

  async create(token: TToken) {
    return this.tokenRepository.create({
      address: token.address,
      name: token.name,
      symbol: token.symbol,
      decimals: token.decimals,
      image: token.image,
    } as Token);
  }

  async getTokensToUpdatePrice() {
    return this.tokenRepository.find({}, {}, { limit: 100, updatedAt: 1 });
  }
}
