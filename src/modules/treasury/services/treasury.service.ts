import { Injectable } from '@nestjs/common';
import { ListAssetDto } from '../dtos/asset.dto';
import { NavRepository } from '../repositories/nav.repository';
import { PipelineStage } from 'mongoose';
import { plainToInstance } from 'class-transformer';
import { NavListResponseDto } from '../dtos/nav.dto';
@Injectable()
export class TreasuryService {
  constructor(private readonly navRepository: NavRepository) {}

  async getAssets(treasuryAddress: string, input: ListAssetDto): Promise<any> {
    const { page, limit, search } = input;
    const pipeline = [
      {
        $match: { treasuryAddress: treasuryAddress, marketValue: { $gt: 1 } },
      },
      {
        $sort: { marketValue: -1 },
      },
      {
        $skip: (page - 1) * limit,
      },
      {
        $limit: limit,
      },
      {
        $lookup: {
          from: 'tokens',
          localField: 'tokenAddress',
          foreignField: 'address',
          as: 'token',
        },
      },
      {
        $unwind: {
          path: '$token',
          preserveNullAndEmptyArrays: true,
        },
      },
      ...(search
        ? [
            {
              $match: {
                'token.name': { $regex: search, $options: 'i' },
              },
            },
          ]
        : []),
      {
        $project: {
          treasuryAddress: 1,
          tokenAddress: 1,
          amount: { $toDouble: '$amount' },
          marketValue: { $toDouble: '$marketValue' },
          tokenName: '$token.name',
          tokenSymbol: '$token.symbol',
          tokenImage: '$token.image',
        },
      },
    ];

    const [result, totalDocs, totalNav] = await Promise.all([
      this.navRepository.aggregate(pipeline as PipelineStage[]),
      this.navRepository.countDocuments({ treasuryAddress: treasuryAddress }),
      this.getTotalNavForAddress(treasuryAddress),
    ]);

    const totalPages = Math.ceil(totalDocs / input.limit);
    const data = {
      docs: result,
      totalNav: totalNav.toString(),
      treasuryAddress,
      totalDocs,
      page: input.page,
      limit: input.limit,
      totalPages,
    };

    return plainToInstance(NavListResponseDto, data, {
      excludeExtraneousValues: true,
    });
  }

  async getTotalNavForAddress(treasuryAddress: string) {
    const totalNav = await this.navRepository.aggregate([
      { $match: { treasuryAddress } },
      { $group: { _id: null, totalNav: { $sum: '$marketValue' } } },
    ]);
    return totalNav[0]?.totalNav || 0;
  }
}
