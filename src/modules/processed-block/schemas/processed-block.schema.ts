import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

export enum EServiceName {
  COLLECT_CURVE_EVENT = 'COLLECT_CURVE_EVENT',
  COLLECT_STAKE_EVENT = 'COLLECT_STAKE_EVENT',
  COLLECT_TOKEN_LOCK_EVENT = 'COLLECT_TOKEN_LOCK_EVENT',
  // EVM Collectors
  COLLECT_CURVE_EVENT_EVM = 'COLLECT_CURVE_EVENT_EVM',
  COLLECT_STAKE_EVENT_EVM = 'COLLECT_STAKE_EVENT_EVM',
  COLLECT_TOKEN_LOCK_EVENT_EVM = 'COLLECT_TOKEN_LOCK_EVENT_EVM',
}

export type ProcessedBlockDocument = ProcessedBlock & Document;

@Schema({ timestamps: true, collection: 'processed_blocks' })
export class ProcessedBlock extends Document {
  @Prop({ required: true, enum: EServiceName })
  serviceName: EServiceName;

  @Prop({ required: true })
  blockNumber: number;

  @Prop({ required: true })
  updatedAt: Date;
}

export const ProcessedBlockSchema = SchemaFactory.createForClass(ProcessedBlock);

ProcessedBlockSchema.plugin(mongoosePaginate);
ProcessedBlockSchema.index({ serviceName: 1 }, { unique: true, background: true });
ProcessedBlockSchema.index({ blockNumber: 1 }, { background: true });
ProcessedBlockSchema.index({ updatedAt: 1 }, { background: true });
