import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { ProcessedBlock, ProcessedBlockDocument, EServiceName } from '../schemas/processed-block.schema';

@Injectable()
export class ProcessedBlockRepository extends BaseRepository<ProcessedBlock> {
  constructor(
    @InjectModel(ProcessedBlock.name) model: PaginateModel<ProcessedBlockDocument>,
  ) {
    super(model);
  }

  async findByServiceName(serviceName: EServiceName) {
    return this.model.findOne({ serviceName });
  }

  async upsertProcessedBlock(serviceName: EServiceName, blockNumber: number) {
    return this.model.findOneAndUpdate(
      { serviceName },
      { 
        serviceName,
        blockNumber,
        updatedAt: new Date()
      },
      { 
        upsert: true,
        new: true
      }
    );
  }

  async getLatestBlockNumber(serviceName: EServiceName): Promise<number | null> {
    const result = await this.model.findOne({ serviceName });
    return result ? result.blockNumber : null;
  }
}
