import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ProcessedBlock, ProcessedBlockSchema } from './schemas/processed-block.schema';
import { ProcessedBlockRepository } from './repositories/processed-block.repository';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ProcessedBlock.name, schema: ProcessedBlockSchema },
    ]),
  ],
  providers: [ProcessedBlockRepository],
  exports: [ProcessedBlockRepository],
})
export class ProcessedBlockModule {}
