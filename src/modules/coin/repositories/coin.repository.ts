import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { Coin, CoinDocument } from '../schemas/coin.schema';
import { ConfigService } from '@nestjs/config';
import { toStringBN } from 'src/utils/number.utils';
import { Decimal128 } from 'bson';
@Injectable()
export class CoinRepository extends BaseRepository<Coin> {
  constructor(
    @InjectModel(Coin.name) model: PaginateModel<CoinDocument>,
    private readonly configService: ConfigService,
  ) {
    super(model);
  }

  async getWithoutPairIdCoins(limit: number = 10) {
    return this.model
      .find({
        tokenAddress: {
          $ne: this.configService.get('app.shroTokenAddress'),
        },
        $and: [
          {
            $or: [
              { raidenxPairId: { $exists: false } },
              { raidenxPairId: null },
            ],
          },
          {
            $or: [{ listedAt: { $exists: false } }, { listedAt: null }],
          },
        ],
      })
      .sort({ lastTrade: -1 })
      .limit(limit);
  }

  async getWithoutPairIdBondingCoins(limit: number = 10) {
    return this.model
      .find({
        listedAt: { $exists: true, $ne: null },
        $or: [
          { raidenxCetusPairId: { $exists: false } },
          { raidenxCetusPairId: null },
        ],
      })
      .limit(limit);
  }

  async updatePairId(coinAddress: string, pairId: string) {
    return this.model.findOneAndUpdate(
      { tokenAddress: coinAddress },
      { raidenxPairId: pairId },
    );
  }

  async updateBondingPairId(coinAddress: string, pairId: string) {
    return this.model.findOneAndUpdate(
      {
        tokenAddress: coinAddress,
        listedAt: { $exists: true, $ne: null },
      },
      { raidenxCetusPairId: pairId },
    );
  }

  async updateRaidenxData(
    coinAddress: string,
    totalHolder: number,
    mcapUsd: string,
    volumeUsd24h: string,
  ) {
    console.log('updateRaidenxData', {
      coinAddress,
      totalHolder,
      volumeUsd24h,
      mcapUsd: Decimal128.fromString(toStringBN(mcapUsd)),
    });
    return this.model.findOneAndUpdate(
      { tokenAddress: coinAddress },
      {
        totalHolder,
        mcapUsd: Decimal128.fromString(toStringBN(mcapUsd)),
        volumeUsd24h: Decimal128.fromString(toStringBN(volumeUsd24h)),
        raidenxUpdatedAt: Date.now(),
      },
    );
  }

  async getTotalVolumeUsd24h() {
    const volumeUsd24h = await this.model.aggregate([
      { $group: { _id: null, totalVolumeUsd24h: { $sum: '$volumeUsd24h' } } },
    ]);
    return volumeUsd24h[0].totalVolumeUsd24h;
  }
}
