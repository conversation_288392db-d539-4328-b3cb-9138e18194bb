import { PaginateResult } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class PaginatedResponseDto<T> implements PaginateResult<T> {
  @ApiProperty()
  @Expose()
  docs: T[];

  @ApiProperty()
  @Expose()
  totalDocs: number;

  @ApiProperty()
  @Expose()
  limit: number;

  @ApiProperty()
  @Expose()
  hasPrevPage: boolean;

  @ApiProperty()
  @Expose()
  hasNextPage: boolean;

  @ApiProperty()
  @Expose()
  page?: number;

  @ApiProperty()
  @Expose()
  totalPages: number;

  @ApiProperty()
  @Expose()
  offset: number;

  @ApiProperty()
  @Expose()
  prevPage?: number | null;

  @ApiProperty()
  @Expose()
  nextPage?: number | null;

  @ApiProperty()
  @Expose()
  pagingCounter: number;

  @ApiProperty()
  @Expose()
  meta?: any;

  [customLabel: string]: T[] | number | boolean | null | undefined;

  constructor(result: PaginateResult<any>) {
    Object.assign(this, result);
  }
}
