// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import {
  AggregatePaginateModel,
  AggregatePaginateResult,
  Document,
  FilterQuery,
  Model,
  PaginateModel,
  PaginateOptions,
  PipelineStage,
  PopulateOptions,
  QueryOptions,
  SaveOptions,
  UpdateQuery,
  UpdateWithAggregationPipeline,
} from 'mongoose';
import { IPagination } from '../shared.interface';

export abstract class BaseRepository<T> {
  public model: Model<T & Document>;
  public modelPagination: PaginateModel<T & Document>;
  AggregatePaginateModel;

  constructor(model: Model<T & Document> | PaginateModel<T & Document>) {
    this.model = model;
    if (model.hasOwnProperty('paginate')) {
      this.modelPagination = model as PaginateModel<T & Document>;
      this.modelAggregatePaginate = model as AggregatePaginateModel<
        T & Document
      >;
    }
  }

  getPaginateRes(paginateOptions: IPagination, data: any) {
    if (!data) {
      data = [];
    }
    return {
      page: paginateOptions.page,
      limit: paginateOptions.limit,
      total_items: data.length,
      data: data as T[],
    };
  }

  async findOne(
    filter?: FilterQuery<T & Document>,
    projection?: Partial<Record<keyof T, 1 | 0>>,
    options?: QueryOptions,
  ): Promise<T & Document> {
    return this.model.findOne(filter, projection, options).lean();
  }

  async create(doc: T, options?: SaveOptions) {
    return new this.model(doc).save(options);
  }

  async createMany(docs: T[], options?: SaveOptions) {
    return await this.model.insertMany(docs, options);
  }

  async updateOne(
    filter?: FilterQuery<T & Document>,
    update?: UpdateQuery<T & Document> | UpdateWithAggregationPipeline,
    options?: QueryOptions,
  ): Promise<boolean> {
    return this.model.updateOne(filter, update, options);
  }

  async findOneAndDelete(
    filter?: FilterQuery<T & Document>,
    options?: QueryOptions | null,
  ): Promise<T & Document> {
    return this.model.findOneAndDelete(filter, options);
  }

  async find(
    filter?: FilterQuery<T & Document>,
    projection?: Partial<Record<keyof T, 1 | 0>>,
    options?: QueryOptions | null,
  ) {
    return this.model.find(filter, projection, options);
  }

  async deleteOne(
    filter?: FilterQuery<T & Document>,
    options?: QueryOptions,
  ): Promise<boolean> {
    const raw = await this.model.deleteOne(filter, options);
    return !!raw.deletedCount;
  }

  async countDocuments(
    filter?: FilterQuery<T & Document>,
    options?: QueryOptions,
  ): Promise<number> {
    return this.model.countDocuments(filter, options);
  }

  async findOneAndUpdate(
    filter?: FilterQuery<T & Document>,
    update?: UpdateQuery<T & Document>,
    options?: QueryOptions | null,
  ): Promise<T & Document> {
    return await this.model.findOneAndUpdate(filter, update, options);
  }

  async paginate(
    filter?: FilterQuery<T & Document>,
    options: {
      page?: number;
      limit?: number;
      sort?: any;
      populate?: PopulateOptions | Array<PopulateOptions>;
      lean?: boolean;
      options?: QueryOptions;
    } = { page: DEFAULT_PAGE, limit: DEFAULT_LIMIT, lean: true },
  ) {
    return this.modelPagination.paginate(filter, options);
  }

  async aggregate(pipeline: PipelineStage[]) {
    return this.model.aggregate(pipeline);
  }

  async aggregatePagination(
    pipeline: PipelineStage[],
    options?: PaginateOptions,
  ): Promise<AggregatePaginateResult<any>> {
    return await this.modelAggregatePaginate.aggregatePaginate(
      pipeline,
      options,
    );
  }

  async updateMany(
    filter?: FilterQuery<T & Document>,
    update?: UpdateWithAggregationPipeline | UpdateQuery<T & Document>,
    options?: QueryOptions,
  ) {
    const raw = await this.model.updateMany(filter, update, options);
    return !!raw.modifiedCount;
  }

  async deleteMany(filter?: FilterQuery<T & Document>, options?: QueryOptions) {
    const raw = await this.model.deleteMany(filter, options);
    return !!raw.deletedCount;
  }

  async distinct(field: string, filter?: FilterQuery<T & Document>) {
    return this.model.distinct(field, filter);
  }
}
