const DEFAULT_SERVER_PORT = 3000;
const DEFAULT_WS_SERVER_PORT = 3001;

console.log('EVM_RPC_URL:', process.env.EVM_RPC_URL);
console.log('SHRO_TOKEN_ADDRESS:', process.env.SHRO_TOKEN_ADDRESS);
console.log('SHRO_TREASURY_ADDRESS:', process.env.SHRO_TREASURY_ADDRESS);
console.log(
  'SHRO_STAKING_POOL_ADDRESS:',
  process.env.SHRO_STAKING_POOL_ADDRESS,
);

export default () => ({
  app: {
    port: parseInt(process.env.APP_PORT as string, 10) || DEFAULT_SERVER_PORT,
    ws_port:
      parseInt(process.env.WS_PORT as string, 10) || DEFAULT_WS_SERVER_PORT,
    prefix: process.env.APP_PREFIX,
    env: process.env.NODE_ENV || 'local',
    evmRpcUrl: process.env.EVM_RPC_URL || 'https://rpc.hyperliquid.xyz/evm',
    shroTokenAddress:
      process.env.SHRO_TOKEN_ADDRESS ||
      '0x7718F3529425E09bF103e5dcf35B9BbB2148f420',
    moonbagsLaunchpadAddress:
      process.env.MOONBAGS_LAUNCHPAD_ADDRESS ||
      '0xda6fCCed61457224e65927b373f57Dae98bF50a7',
    moonbagsStakeAddress:
      process.env.MOONBAGS_STAKE_ADDRESS ||
      '0x0000000000000000000000000000000000000000', // To be updated
    tokenLockAddress:
      process.env.TOKEN_LOCK_ADDRESS ||
      '0x0000000000000000000000000000000000000000', // To be updated
    shroTreasuryAddress: process.env.SHRO_TREASURY_ADDRESS,
    shroStakingPoolAddress: process.env.SHRO_STAKING_POOL_ADDRESS,
  },
});
