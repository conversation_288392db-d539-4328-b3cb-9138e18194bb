export default () => ({
  twitter: {
    consumerKey: process.env.TWITTER_CLIENT_ID,
    consumerSecret: process.env.TWITTER_CLIENT_SECRET,
    verifyURL: 'https://api.twitter.com/oauth/access_token',
  },
  auth: {
    jwtSecret: process.env.JWT_SECRET_KEY || 'moonbags-secret-key',
    jwtExpireIn: process.env.JWT_ACCESS_TOKEN_EXPIRATION_TIME || '300', // 1h
    refreshTokenSecret:
      process.env.JWT_REFRESH_TOKEN_SECRET_KEY || 'moonbags-refresh-secret-key',
    refreshTokenExpireIn:
      process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME || '604800',
  },
});
