import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { SharedModule } from '@modules/shared/shared.module';
import { CommentModule } from '@modules/comment/comment.module';
import { AuthModule } from '@modules/shared/auth/auth.module';
import { CandleModule } from '@modules/candle/candle.module';
import { CoinModule } from '@modules/coin/coin.module';
import { TradeModule } from '@modules/trade/trade.module';
import { StakingModule } from '@modules/staking/staking.module';
import { TreasuryModule } from '@modules/treasury/treasury.module';
import { CoinLockModule } from '@modules/coin-lock/coin-lock.module';
import { HolderModule } from '@modules/holder/holder.module';
import { EventLogModule } from '@modules/event-log/event-log.module';
import { ProcessedBlockModule } from '@modules/processed-block/processed-block.module';
// import { ThrottlerModule } from '@nestjs/throttler';
// import { AppThrottlerGuard } from '@shares/guards';
// import { ConfigService } from '@nestjs/config';
// import { ConfigModule } from '@nestjs/config';
// import { ThrottlerStorageRedisService } from '@nest-lab/throttler-storage-redis';

@Module({
  imports: [
    // ThrottlerModule.forRootAsync({
    //   imports: [ConfigModule],
    //   inject: [ConfigService],
    //   useFactory: (configService: ConfigService) => [
    //     {
    //       ttl: configService.get<number>('REDIS_TTL', 60000), // 1 minute default
    //       limit: configService.get<number>('REDIS_LIMIT', 100), // 100 requests per minute default
    //       storage: new ThrottlerStorageRedisService(
    //         configService.get<string>('redis.url'),
    //       ),
    //     },
    //   ],
    // }),
    SharedModule,
    CommentModule,
    AuthModule,
    CoinModule,
    TradeModule,
    CandleModule,
    StakingModule,
    TreasuryModule,
    CoinLockModule,
    HolderModule,
    EventLogModule,
    ProcessedBlockModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
