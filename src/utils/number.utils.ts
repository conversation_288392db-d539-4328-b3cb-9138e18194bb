import { BigNumber } from 'bignumber.js';
import { Decimal128 } from 'bson';

BigNumber.config({
  EXPONENTIAL_AT: 24,
});

const DEFAULT_DECIMALS_FORMAT = 16;

export const multiplyBN = (
  value1: number | string | BigNumber | Decimal128,
  value2: number | string | BigNumber | Decimal128,
  decimals: number = DEFAULT_DECIMALS_FORMAT,
  roundingMode?: BigNumber.RoundingMode,
): string => {
  const toFixed = new BigNumber(value1?.toString() || 0)
    .multipliedBy(value2?.toString() || 0)
    .toFixed(decimals, roundingMode);
  return new BigNumber(toFixed).toString();
};

export const divideBN = (
  value1: number | string | BigNumber | Decimal128,
  value2: number | string | BigNumber | Decimal128,
  decimals: number = DEFAULT_DECIMALS_FORMAT,
  roundingMode?: BigNumber.RoundingMode,
): string => {
  const toFixed = new BigNumber(value1?.toString() || 0)
    .dividedBy(value2?.toString() || 0)
    .toFixed(decimals, roundingMode);
  return new BigNumber(toFixed).toString();
};

export const plusBN = (
  value1: number | string | BigNumber | Decimal128,
  value2: number | string | BigNumber | Decimal128,
  decimals: number = DEFAULT_DECIMALS_FORMAT,
  roundingMode?: BigNumber.RoundingMode,
): string => {
  const toFixed = new BigNumber(value1?.toString() || 0)
    .plus(value2?.toString() || 0)
    .toFixed(decimals, roundingMode);
  return new BigNumber(toFixed).toString();
};

export const minusBN = (
  value1: number | string | BigNumber | Decimal128,
  value2: number | string | BigNumber | Decimal128,
  decimals: number = DEFAULT_DECIMALS_FORMAT,
  roundingMode?: BigNumber.RoundingMode,
): string => {
  const toFixed = new BigNumber(value1?.toString() || 0)
    .minus(value2?.toString() || 0)
    .toFixed(decimals, roundingMode);
  return new BigNumber(toFixed).toString();
};

export const minBN = (
  ...values: (number | string | BigNumber | Decimal128)[]
): string => {
  return BigNumber.min(
    ...values.map((value) => value?.toString() || 0),
  ).toString();
};

export const maxBN = (
  ...values: (number | string | BigNumber | Decimal128)[]
): string => {
  return BigNumber.max(
    ...values.map((value) => value?.toString() || 0),
  ).toString();
};

export const toStringBN = (
  value: string | number | BigNumber | Decimal128,
  decimals: number = DEFAULT_DECIMALS_FORMAT,
  roundingMode?: BigNumber.RoundingMode,
): string => {
  const toFixed = new BigNumber(value?.toString() || 0).toFixed(
    decimals,
    roundingMode,
  );
  return new BigNumber(toFixed).toString();
};

export const toNumberBN = (
  value: string | number | BigNumber | Decimal128,
  decimals: number = DEFAULT_DECIMALS_FORMAT,
  roundingMode?: BigNumber.RoundingMode,
): number => {
  const toFixed = new BigNumber(value?.toString() || 0).toFixed(
    decimals,
    roundingMode,
  );
  return new BigNumber(toFixed).toNumber();
};

export const isZeroBN = (
  value: string | number | BigNumber | Decimal128,
): boolean => {
  return new BigNumber(value?.toString() || 0).isZero();
};

export const isPositiveBN = (
  value: string | number | BigNumber | Decimal128,
): boolean => {
  return new BigNumber(value?.toString() || 0).isPositive();
};

export const toIntegerValueBn = (
  value: string | number | BigNumber | Decimal128,
  rm?: BigNumber.RoundingMode,
): string => {
  return new BigNumber(value?.toString() || 0).integerValue(rm).toString();
};
