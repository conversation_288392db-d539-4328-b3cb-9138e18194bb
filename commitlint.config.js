module.exports = {
    extends: ['@commitlint/config-conventional'],
    rules: {
      'type-enum': [
        2,
        'always',
        [
          'feat', // New feature
          'fix', // Bug fix
          'improve', // Code improvement
          'refactor', // Code refactoring
          'docs', // Documentation
          'chore', // Minor changes in development process
          'style', // Code style changes, formatting, no impact on logic
          'test', // Adding tests
          'revert', // Revert to a previous commit
          'ci', // CI/CD configuration changes
          'build', // Build-related changes
        ]
      ],
    },
  };

