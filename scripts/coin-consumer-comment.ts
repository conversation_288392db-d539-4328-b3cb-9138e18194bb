import { SharedModule } from '@modules/shared/shared.module';
import { Module, Injectable } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { BaseConsumer } from 'src/shares/base.consumer';
import { KafkaMessage } from 'kafkajs';
import { CoinModule } from '@modules/coin/coin.module';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import * as _ from 'lodash';
import { ConfigService } from '@nestjs/config';
import { KafkaTopic } from 'src/shares/enums/kafka.enum';
interface CommentMessage {
  tokenAddress: string;
  walletAddress: string;
  timestamp: number;
}

@Injectable()
export class CoinConsumerComment extends BaseConsumer {
  constructor(
    private readonly coinRepository: CoinRepository,
    configService: ConfigService,
  ) {
    super(configService);
  }

  subscribeTopic(): string | RegExp {
    return KafkaTopic.COMMENT_CREATED;
  }

  async handleBatch(messages: KafkaMessage[]) {
    // Group messages by tokenAddress
    const groupedMessages = _.groupBy(
      messages.map((message) => {
        return JSON.parse(message.value.toString()) as CommentMessage;
      }),
      'tokenAddress',
    );

    // For each tokenAddress, sort by timestamp and get latest message
    for (const [tokenAddress, comments] of Object.entries(groupedMessages)) {
      const sortedComments = _.orderBy(comments, ['timestamp'], ['desc']);
      const latestComment = sortedComments[0];

      // Update coin schema with latest trade info
      await this.coinRepository.updateOne(
        { tokenAddress },
        {
          lastReply: latestComment.timestamp,
        },
      );
    }
  }
}

@Module({
  imports: [SharedModule, CoinModule],
  providers: [CoinConsumerComment],
})
export class AppModule {}

const run = async () => {
  const app = await NestFactory.create(AppModule);
  const consumer = app.get(CoinConsumerComment);
  await consumer.run();
};

run().catch((error) => {
  console.error(error.message);
  console.error(
    `Something went wrong. Process will be restart shortly... with error:${error.toString()}`,
  );
  process.exit(1);
});
