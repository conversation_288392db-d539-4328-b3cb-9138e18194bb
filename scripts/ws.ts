import { config } from 'dotenv';
config();
import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { WebSocketModule } from '@modules/websocket/websocket.module';
import { RedisIoAdapter } from '@modules/websocket/adapters/redis.adapter';

async function bootstrap() {
  const app = await NestFactory.create(WebSocketModule);
  app.enableCors();
  const configService = app.get(ConfigService);
  const port = configService.get('app.ws_port');
  const redisIoAdapter = new RedisIoAdapter(app);
  await redisIoAdapter.connectToRedis();
  app.useWebSocketAdapter(redisIoAdapter);
  await app.listen(port, () => {
    console.log(`Server Websocket running on http://localhost:${port}`);
  });
}
bootstrap();
