/* eslint-disable @typescript-eslint/no-unused-vars */
import * as dotenv from 'dotenv';
dotenv.config();
import { NestFactory } from '@nestjs/core';
import { Injectable, Module } from '@nestjs/common';
import { SharedModule } from '@modules/shared/shared.module';
import { sleep } from '@shares/utils/common.utils';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import axios from 'axios';
import { CoinModule } from '@modules/coin/coin.module';

const RAIDENX_API_URL = process.env.RAIDENX_API_URL || 'https://api.raidenx.io';
const BOOSTED_MOONBAGS_API_URL = `${RAIDENX_API_URL}/api/v1/evm/tokens/boosted-moonbags`;

@Injectable()
export class IndexerBoostedCoins {
  constructor(private readonly coinRepository: CoinRepository) {}

  async run() {
    while (true) {
      try {
        await this.processBoostedCoins();
      } catch (error) {
        console.error('Error processing boosted coins:', error?.message);
      }

      console.log('Sleep 5 minutes to next tick');
      await sleep(5 * 60 * 1000);
    }
  }

  private async processBoostedCoins() {
    const response = await axios.get(BOOSTED_MOONBAGS_API_URL);
    const boostedMoonbags = response.data;

    if (!boostedMoonbags || boostedMoonbags.length === 0) {
      console.log('No boosted moonbags found');
      return;
    }

    const updates = boostedMoonbags.map((moonbag) => ({
      updateOne: {
        filter: { tokenAddress: moonbag?.address },
        update: {
          $set: {
            isBoostedUntil: moonbag?.isBoostedUntil
              ? new Date(
                  moonbag?.isBoostedUntil?.replace('+00:00:00', '').trim(),
                )
              : null,
            boostFactor: moonbag?.boostFactor || 0,
          },
        },
      },
    }));

    if (updates.length > 0) {
      await this.coinRepository.model.bulkWrite(updates);
      console.log(`Updated ${updates.length} boosted coins`);
    }
  }
}

@Module({
  imports: [SharedModule, CoinModule],
  providers: [IndexerBoostedCoins],
  controllers: [],
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.init();
  const indexer = app.get(IndexerBoostedCoins);
  await indexer.run();
}

bootstrap().catch((error) => {
  console.error(error);
  process.exit(1);
});
