/* eslint-disable @typescript-eslint/no-unused-vars */
import { SharedModule } from '@modules/shared/shared.module';
import { NavRepository } from '@modules/treasury/repositories/nav.repository';
import { TokenRepository } from '@modules/treasury/repositories/token.repository';
import { TreasuryModule } from '@modules/treasury/treasury.module';
import { Injectable, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { sleep } from '@shares/utils/common.utils';
import axios from 'axios';
import BigNumber from 'bignumber.js';
import * as dotenv from 'dotenv';
dotenv.config();

@Injectable()
export class IndexerTreasury {
  constructor(
    private readonly navRepository: NavRepository,
    private readonly tokenRepository: TokenRepository,
    private readonly configService: ConfigService,
  ) {}

  async run() {
    const SHRO_TREASURY_ADDRESS = this.configService.get<string>(
      'app.shroTreasuryAddress',
    );
    while (true) {
      await this.processTreasuryAddress(SHRO_TREASURY_ADDRESS);

      console.log('Sleep 5 seconds to next tick');
      await sleep(5000);
    }
  }

  private async processTreasuryAddress(treasuryAddress: any) {
    console.log(`Processing treasuryAddress: ${treasuryAddress}`);
    const treasuryBalances = await this.getTreasuryBalances(treasuryAddress);

    const navUpdates = [];
    const newTokens = [];

    for (const balance of treasuryBalances) {
      console.log(
        `balance.coinType: ${balance.coinType} with amount: ${balance.totalBalance}`,
      );
      const existToken = await this.getToken(balance.coinType);
      let amount;
      if (existToken) {
        amount = this.calculateAmount(
          balance.totalBalance,
          existToken.decimals,
        );
      } else {
        amount = this.calculateAmount(balance.totalBalance, balance.decimals);

        newTokens.push({
          address: balance.coinType,
          name: balance.name,
          symbol: balance.symbol,
          decimals: balance.decimals,
          image: balance.iconUrl ?? null,
        });
      }

      navUpdates.push({
        treasuryAddress,
        tokenAddress: balance.coinType,
        amount,
      });
      console.log(
        `balance.coinType: ${balance.coinType} with totalBalance: ${balance.totalBalance}, amount: ${amount}`,
      );
    }

    if (navUpdates.length > 0) {
      await this.navRepository.saveMany(navUpdates);
    }
    if (newTokens.length > 0) {
      await this.tokenRepository.saveMany(newTokens);
    }
  }

  private async getToken(tokenAddress: string) {
    return this.tokenRepository.findOne({
      address: tokenAddress,
      decimals: { $exists: true },
    });
  }

  private calculateAmount(amount: string, decimals: number) {
    return new BigNumber(amount)
      .dividedBy(new BigNumber(10).pow(decimals))
      .toString();
  }

  private async getTreasuryBalances(treasuryAddress: string): Promise<
    {
      coinType: string;
      totalBalance: string;
      decimals: number;
      name: string;
      symbol: string;
      iconUrl: string;
    }[]
  > {
    const response = await axios.get(
      `https://www.hyperscan.com/api/v2/addresses/${treasuryAddress}/tokens?type=ERC-20`,
      {
        headers: {
          accept: 'application/json',
        },
      },
    );
    const tokens = response.data.items || [];

    return tokens.map((token: any) => ({
      coinType: token.token.address,
      totalBalance: token.value,
      decimals: token.decimals,
      name: token.token.name,
      symbol: token.token.symbol,
      iconUrl: token.token.icon_url,
    }));
  }
}

@Module({
  imports: [SharedModule, TreasuryModule],
  providers: [IndexerTreasury],
  controllers: [],
})
class IndexerTreasuryModule {}

async function bootstrap() {
  const app = await NestFactory.create(IndexerTreasuryModule);
  await app.init();
  const indexer = app.get(IndexerTreasury);
  await indexer.run();
}

bootstrap().catch((error) => {
  console.error(error);
  process.exit(1);
});
