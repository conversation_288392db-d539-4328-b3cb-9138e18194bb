/* eslint-disable @typescript-eslint/no-unused-vars */
import * as dotenv from 'dotenv';
dotenv.config();
import { NestFactory } from '@nestjs/core';
import { Injectable, Module } from '@nestjs/common';
import { SharedModule } from '@modules/shared/shared.module';
import { sleep } from '@shares/utils/common.utils';
import { TreasuryModule } from '@modules/treasury/treasury.module';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import { Coin } from '@modules/coin/schemas/coin.schema';
import { formatEvmAddress } from '@shares/utils/evm-utils';
import axios from 'axios';
import { CoinModule } from '@modules/coin/coin.module';

const RAIDENX_API_URL = process.env.RAIDENX_API_URL || 'https://api.raidenx.io';
const TOPPAIR_API_URL = `${RAIDENX_API_URL}/api/v1/sui/tokens/$tokenAddress/top-pair`;

@Injectable()
export class IndexerPairId {
  constructor(private readonly coinRepository: CoinRepository) {}

  async run() {
    while (true) {
      const bondingCoins =
        await this.coinRepository.getWithoutPairIdBondingCoins();
      for (const coin of bondingCoins) {
        await this.processBondingCoin(coin);
        await sleep(500);
      }

      const coins = await this.coinRepository.getWithoutPairIdCoins();
      for (const coin of coins) {
        await this.processCoin(coin);
        await sleep(500);
      }

      console.log('Sleep 5 seconds to next tick');
      await sleep(5000);
    }
  }

  private async processBondingCoin(coin: Coin) {
    const url = TOPPAIR_API_URL.replace(
      '$tokenAddress',
      formatEvmAddress(coin.tokenAddress),
    );
    console.log(
      `Processing bonding coin: ${coin.tokenAddress} with url: ${url}`,
    );
    try {
      const response = await axios.get(url);

      const data = response.data;
      if (
        !!data &&
        ['turbosfinance', 'cetus'].includes(data.dexName) &&
        data.pairId
      ) {
        console.log(
          `Updating bonding pairId for ${coin.tokenAddress} to ${data.pairId}`,
        );
        await this.coinRepository.updateBondingPairId(
          coin.tokenAddress,
          data.pairId,
        );
      }
    } catch (error: any) {
      console.error(
        `Error processing bonding coin: ${coin.tokenAddress}`,
        error?.message,
      );
    }
  }

  private async processCoin(coin: Coin) {
    try {
      const url = TOPPAIR_API_URL.replace(
        '$tokenAddress',
        formatEvmAddress(coin.tokenAddress),
      );
      console.log(`Processing coin: ${coin.tokenAddress} with url: ${url}`);
      const response = await axios.get(url);
      const data = response.data;
      if (!!data && data.dexName === 'moonbags' && data.pairId) {
        console.log(
          `Updating pairId for ${coin.tokenAddress} to ${data.pairId}`,
        );
        await this.coinRepository.updatePairId(coin.tokenAddress, data.pairId);
      }
    } catch (error: any) {
      console.error(
        `Error processing coin: ${coin.tokenAddress}`,
        error?.message,
      );
    }
  }
}

@Module({
  imports: [SharedModule, TreasuryModule, CoinModule],
  providers: [IndexerPairId],
  controllers: [],
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.init();
  const indexer = app.get(IndexerPairId);
  await indexer.run();
}

bootstrap().catch((error) => {
  console.error(error);
  process.exit(1);
});
